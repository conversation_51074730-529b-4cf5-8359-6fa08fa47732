<?php

namespace App\Models;

use App\Traits\HasEasyStatus;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PluginViduTemplate extends Model
{
    use HasEasyStatus;

    const EXOTIC_PRINCESS_AREA = [
        'auto'        => '自动',
        'denmark'     => '丹麦',
        'uk'          => '英国',
        'africa'      => '非洲',
        'china'       => '中国',
        'mexico'      => '墨西哥',
        'switzerland' => '瑞士',
        'russia'      => '俄罗斯',
        'italy'       => '意大利',
        'korea'       => '韩国',
        'thailand'    => '泰国',
        'india'       => '印度',
        'japan'       => '日本',
    ];
    const BEAST_COMPANIONS     = [
        'auto'  => '自动',
        'bear'  => '熊',
        'tiger' => '虎',
        'elk'   => '鹿',
        'snake' => '蛇',
        'lion'  => '狮子',
        'wolf'  => '狼',
    ];

    protected $casts = [
        'detail'            => 'json',
        'input_instruction' => 'json',
        'instructions'      => 'json',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(PluginViduTemplateCategory::class, 'vidu_template_category_id');
    }

    public function getExtraParams()
    {
        if ($this->scene === 'exotic_princess') {
            return $this->getArea();
        } elseif ($this->scene === 'beast_companion') {
            return $this->getBeast();
        }

        return null;
    }

    /**
     * Notes: 获取异域公主数据
     *
     * @Author: 玄尘
     * @Date: 2025/4/18 14:44
     * @return null
     */
    public function getArea()
    {
        if ($this->scene != 'exotic_princess') {
            return null;
        }
        return collect(self::EXOTIC_PRINCESS_AREA)
            ->map(function ($area, $key) {
                return [
                    'value' => $key,
                    'label' => $area
                ];
            })
            ->values()
            ->all();
    }

    public function getBeast()
    {
        if ($this->scene != 'beast_companion') {
            return null;
        }
        return collect(self::BEAST_COMPANIONS)
            ->map(function ($beast, $key) {
                return [
                    'value' => $key,
                    'label' => $beast
                ];
            })
            ->values()
            ->all();
    }

    public function getExtraName()
    {
        if ($this->scene === 'exotic_princess') {
            return 'area';
        } elseif ($this->scene === 'beast_companion') {
            return 'beast';
        }
        return null;
    }

    public function getExtraTitle()
    {
        if ($this->scene === 'exotic_princess') {
            return '国家';
        } elseif ($this->scene === 'beast_companion') {
            return '动物';
        }
        return null;
    }

    public function getInputInstruction()
    {
        $input_instruction = $this->input_instruction;

        // 中文数字映射
        $chineseNumbers = [
            '一' => 1,
            '二' => 2,
            '三' => 3,
            '四' => 4,
            '五' => 5,
            '六' => 6,
            '七' => 7,
            '八' => 8,
            '九' => 9,
            '十' => 10
        ];

        // 提取image_count中的中文数字
        if (isset($input_instruction['image_count'])) {
            // 使用更简单的方式匹配中文数字
            preg_match('/[一二三四五六七八九十]/u', $input_instruction['image_count'], $matches);
            $input_instruction['image_count_value'] = isset($matches[0]) && isset($chineseNumbers[$matches[0]])
                ? $chineseNumbers[$matches[0]]
                : 1;
        } else {
            $input_instruction['image_count_value'] = 1;
        }

        return $input_instruction;
    }

    public function getDetail()
    {
        $detail                = $this->detail;
        $detail['credit_cost'] = $this->score ? $this->score : PluginViduDraw::SCORE_VIDEO;
        return $detail;
    }

    public function getAspectRatios()
    {
        $detail      = $this->detail;
        $aspectRatio = explode('、', $detail['aspect_ratio']);
        return $aspectRatio;
    }

}
