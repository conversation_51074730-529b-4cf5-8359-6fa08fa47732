<?php

namespace App\Jobs\Assistant;

use AlibabaCloud\SDK\Dm\V20151123\Dm;
use AlibabaCloud\SDK\Dm\V20151123\Models\SingleSendMailRequest;
use App\Jobs\BaseJob;
use App\Models\User;
use App\Packages\AlibabaCloud\Alibaba;
use Darabonba\OpenApi\Models\Config;
use Illuminate\Support\Str;

class SendEmailToolJob extends BaseJob
{
    public int $timeout = 120;

    public function __construct(
        protected User $user,
        protected string $email,
        protected string $title,
        protected string $content,
        protected string $model,
        protected int $maxToken = 4096
    ) {
    }

    public function handle(): void
    {
        $content               = Alibaba::aichat()->volcengine()
            ->chatFormPrompt('EmailContentOptimization', $this->model, $this->content, $this->maxToken);
        $config                = new Config([
            "accessKeyId"     => config("user.SMS_ALIYUN_ACCESS_KEY_ID"),
            "accessKeySecret" => config("user.SMS_ALIYUN_ACCESS_KEY_SECRET")
        ]);
        $config->endpoint      = "dm.aliyuncs.com";
        $client                = new Dm($config);
        $singleSendMailRequest = new SingleSendMailRequest([
            "accountName"    => "<EMAIL>",
            "addressType"    => 1,
            "replyToAddress" => false,
            "toAddress"      => $this->email,
            "subject"        => Str::limit($this->title, 100, ''),
            "htmlBody"       => $content,
            "fromAlias"      => $this->user->info->nickname,
        ]);
        $client->singleSendMail($singleSendMailRequest);
    }
}