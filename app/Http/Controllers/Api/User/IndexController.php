<?php

namespace App\Http\Controllers\Api\User;

use AlibabaCloud\SDK\Dm\V20151123\Dm;
use AlibabaCloud\SDK\Dm\V20151123\Models\SingleSendMailRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use App\Http\Controllers\Controller;
use App\Models\AiUnifyAsset;
use App\Models\User;
use Darabonba\OpenApi\Models\Config;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Modules\Interaction\Models\Like;

class IndexController extends Controller
{

    public function signOut(Request $request)
    {
        try {
            $user           = $request->kernel->user();
            $count          = User::where('username', 'like',
                "{$user->username}_%")
                ->count();
            $user->username = $user->username."_".($count + 1);
            $user->save();
            $user->wechat()->update([
                'wx_openid'   => null,
                'mini_openid' => null,
                'union_id'    => null,
            ]);
            return $request->kernel->success([], '注销成功');
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

    public function getUserInfo(Request $request)
    {
        $userPublish = AiUnifyAsset::ofUser($request->kernel->id())
            ->pluck('id')->toArray() ?: [];

        $likes = Like::where('likeable_type', app(AiUnifyAsset::class)->getMorphClass())
            ->whereIn('likeable_id', $userPublish)
            ->count('id');
        $user  = User::withCount([
            'fans',
            'follow',
        ])->find($request->kernel->id());

        $identity = $user->identities->first();

        return $request->kernel->success([
            'phone'           => $user->getMobile(),
            'email'           => $user->email,
            'nickname'        => $user->info->nickname,
            'avatar'          => $user->info->avatar_url,
            'description'     => $user->info->description,
            'balance'         => $user->account->score,
            'balance_buy'     => '0.00',
            'balance_other'   => $user->account->score,
            'identity'        => [
                'is_vip'      => $identity?->id > 1,
                'identity_id' => $identity?->id,
                'name'        => $identity?->name,
                'icon'        => $identity?->cover_url,
                'expire_time' => $identity?->pivot->ended_at,
                'rules'       => collect($identity?->rules)->whereNotNull('desc')
                    ->values()->toArray(),
            ],
            'vip_expire_time' => '',
            'knowledge'       => $user->knowledges()->count(),
            'openid'          => $user->getWechatOpenid(),
            'unionid'         => $user->getUnionId(),
            'realname'        => $user->realName->nickname ?? '',
            'user_id'         => $user->id,
            'is_real'         => $user->realName->is_check ?? 0,
            'is_show'         => 1,
            'default_company' => '',
            'recommend'       => '',
            'pass'            => $user->pass,
            'im_push'         => [
                'im_user_id' => $user->imUser->im_user_id ?? '',
                'user_sig'   => $user->imUser ? $user->imUser->getUserSig() : '',
            ],
            'interaction'     => [
                'interaction_type' => $user->getMorphClass(),
                'interaction_id'   => $user->getKey(),
                'followed'         => false,
                'fans'             => $user->fans_count,
                'follow'           => $user->follow_count,
                'used'             => 0,
                'like'             => $likes,
            ],
        ]);
    }

    public function setUserInfo(Request $request)
    {
        $user     = $request->kernel->user();
        $userInfo = [];
        $userPass = $request->pass;
        if ($request->nickname) {
            $userInfo['nickname'] = $request->nickname;
        }
        if ($request->avatar) {
            $userInfo['avatar'] = $request->avatar;
        }
        if ($request->description) {
            $userInfo['description'] = $request->description;
        }
        if ($userPass) {
            $user->pass = $request->pass;
            $user->save();
        }
        if (count($userInfo) > 0) {
            $user->info()->update($userInfo);
        }
        return $request->kernel->success([]);
    }

    public function emailCode(Request $request)
    {
        $request->kernel->validate([
            'email' => 'required|email',
        ], [
            'email.required' => '请输入邮箱',
            'email.email'    => '请输入正确的邮箱',
        ]);
        $email   = $request->email;
        $hasUser = User::where('email', $email)->first();
        $user    = $request->kernel->user();

        if ($hasUser) {
            if ($hasUser->id != $user->id) {
                return $request->kernel->error('邮箱已绑定其他账号');
            } else {
                return $request->kernel->error('不可重复绑定同一个邮箱');
            }
        }

        $code = sprintf("%06d", mt_rand(1, 999999));
        try {
            $this->sendEmail($email, $code);
            Cache::put("email_code_{$user->id}_{$email}", $code, 60 * 5);
            return $request->kernel->success([], '发送成功');
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

    private function sendEmail(string $email, string $code)
    {
        $config = new Config([
            "accessKeyId"     => config("user.SMS_ALIYUN_ACCESS_KEY_ID"),
            "accessKeySecret" => config("user.SMS_ALIYUN_ACCESS_KEY_SECRET")
        ]);

        $config->endpoint      = "dm.aliyuncs.com";
        $client                = new Dm($config);
        $singleSendMailRequest = new SingleSendMailRequest([
            "accountName"    => "<EMAIL>",
            "addressType"    => 1,
            "replyToAddress" => false,
            "toAddress"      => $email,
            "subject"        => '邮箱绑定验证码 - Email binding verification code',
            "htmlBody"       => view('email.code', compact('code'))->render(),
            "fromAlias"      => '瓦特AI(Watt AI)',
        ]);
        $runtime               = new RuntimeOptions([
            'ignoreSSL' => true,
        ]);

        $response = $client->singleSendMailWithOptions($singleSendMailRequest, $runtime);
        if ($response->statusCode == 200) {
            return true;
        } else {
            throw new Exception('邮件发送失败');
        }
    }

    public function checkEmailCode(Request $request)
    {
        $request->kernel->validate([
            'email' => 'required|email',
            'code'  => 'required',
        ], [
            'email.required' => '请输入邮箱',
            'email.email'    => '请输入正确的邮箱',
            'code.required'  => '请输入验证码',
        ]);
        $email   = $request->email;
        $hasUser = User::where('email', $email)->first();
        $user    = $request->kernel->user();

        if ($hasUser) {
            if ($hasUser->id != $user->id) {
                return $request->kernel->error('邮箱已绑定其他账号');
            } else {
                return $request->kernel->error('不可重复绑定同一个邮箱');
            }
        }
        $code       = $request->code;
        $verifyCode = Cache::get("email_code_{$user->id}_{$email}", '');
        if ($code != $verifyCode) {
            return $request->kernel->error('验证码错误');
        }
        $user->email = $email;
        $user->save();
        return $request->kernel->success([], '绑定成功');
    }
}