<?php

use App\Http\Controllers\Api\Knowledge\AboutController;
use App\Http\Controllers\Api\User\AccountController;
use App\Http\Controllers\Api\User\IdentityController;
use App\Http\Controllers\Api\User\IndexController;
use App\Http\Controllers\Api\User\PhoneController;
use App\Http\Controllers\Api\User\PrivacyController;
use App\Http\Controllers\Api\User\RealnameController;
use App\Http\Controllers\Api\User\SignController;
use App\Http\Controllers\Api\User\WorkCardController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('user/info', [IndexController::class, 'getUserInfo'])
        ->name('user.getUserinfo');
    $router->post('user/set', [IndexController::class, 'setUserInfo'])
        ->name('user.saveUserInfo');
    $router->post('user/signOut', [IndexController::class, 'signOut'])
        ->name('user.signOut');

    $router->post('user/bindPhone', [PhoneController::class, 'bindPhone'])
        ->name('user.bindPhone');
    $router->post('user/changePhone', [PhoneController::class, 'changePhone'])
        ->name('user.changePhone');

    $router->post('user/realName',
        [RealnameController::class, 'verify_realname'])->name('user.realname');
    $router->post('user/browse', [PrivacyController::class, 'browse'])
        ->name('user.privacy');
    $router->post('user/setting', [PrivacyController::class, 'setting'])
        ->name('user.setPrivacy');
    $router->post('user/setRank', [PrivacyController::class, 'setRank'])
        ->name('user.setRank');
    $router->post('user/getWorkCard', [WorkCardController::class, 'index'])
        ->name('user.getWork');
    #充值账变
    $router->post('user/order/lists', [AccountController::class, 'orders'])
        ->name('order.getList');
    $router->post('user/account/init', [AccountController::class, 'logsInit'])
        ->name('order.useOrderLogInit');
    $router->post('user/account/logs', [AccountController::class, 'logs'])
        ->name('order.useOrderLog');

    $router->post('user/emailCode', [IndexController::class, 'emailCode'])
        ->name('user.emailCode');
    $router->post('user/checkEmailCode', [IndexController::class, 'checkEmailCode'])
        ->name('user.checkEmailCode');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'account'
], function (Router $router) {
    $router->post('scoreBalance', [AccountController::class, 'scoreBalance'])
        ->name('user.Account.scoreBalance');
});
Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('user/identity/openList', [IdentityController::class, 'openList'])->name('identity.openList');
    $router->post('user/identity/open', [IdentityController::class, 'open'])->name('identity.open');
    $router->post('user/identity/verify', [IdentityController::class, 'verify'])->name('identity.apple-verify');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
], function (Router $router) {
    $router->post('user/know', [AboutController::class, 'index'])
        ->name('user.know');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'user/sign'
], function (Router $router) {
    $router->post('index', [SignController::class, 'index'])
        ->name('user.Sign.index');
    $router->post('do', [SignController::class, 'sign'])
        ->name('user.Sign.do');
});
