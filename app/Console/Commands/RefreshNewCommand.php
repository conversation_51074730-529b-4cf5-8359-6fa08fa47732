<?php

namespace App\Console\Commands;

use App\Jobs\Search\RefreshNewsJob;
use Illuminate\Console\Command;

class RefreshNewCommand extends Command
{

    protected $signature   = 'search:news';
    protected $description = '刷新豆包的新闻';

    protected array $category = [
        '100001',
        '100008',
        '100002',
        '100006',
        '100003',
        '100004',
        '100000',
        '100009',
        '100005',
        '100007',
    ];

    public function handle()
    {
        foreach ($this->category as $item) {
            RefreshNewsJob::dispatch($item);
        }
    }
}