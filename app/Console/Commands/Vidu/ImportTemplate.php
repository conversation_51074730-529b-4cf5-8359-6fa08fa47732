<?php

namespace App\Console\Commands\Vidu;

use App\Models\PluginViduTemplate;
use App\Models\PluginViduTemplateCategory;
use App\Models\SystemConfig;
use Exception;
use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImportTemplate extends Command
{
    protected     $signature      = 'skyxu:vidu-import-templates';
    protected     $description    = '采集模板数据';
    protected int $batchSize      = 20;
    protected int $totalProcessed = 0;

    public function handle(): void
    {
        $pageToken = '';
        $hasMore   = true;

        try {
            $cookie = SystemConfig::getValue('vidu_cookie');
            if (! $cookie) {
                $this->error('vidu_cookie 为空');
                return;
            }

            $client = new GuzzleClient([
                'headers' => [
                    'accept'             => '*/*',
                    'accept-encoding'    => 'gzip, deflate, br, zstd',
                    'accept-language'    => 'zh-CN,zh;q=0.9',
                    'origin'             => 'https://platform.vidu.cn',
                    'referer'            => 'https://platform.vidu.cn/',
                    'sec-ch-ua'          => '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                    'sec-ch-ua-mobile'   => '?0',
                    'sec-ch-ua-platform' => '"Windows"',
                    'sec-fetch-dest'     => 'empty',
                    'sec-fetch-mode'     => 'cors',
                    'sec-fetch-site'     => 'same-site',
                    'user-agent'         => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'cookie'             => $cookie
                ]
            ]);

            $this->info("开始导入vidu场景模板");

            // 先获取第一页以确定总数
            $firstPageUrl  = 'https://api.vidu.cn/ent/v1/template-scenes?pager.page_token=&pager.pagesz='.$this->batchSize;
            $firstResponse = $client->get($firstPageUrl);
            $firstData     = json_decode($firstResponse->getBody()->getContents(), true);
            // 获取总数量
            $totalCount  = $firstData['total'] ?? 0;
            $progressBar = $this->output->createProgressBar($totalCount);
            $progressBar->setFormat(
                '当前%current%/%max% [%bar%] %percent:3s%% '.
                "\n进度: ID:%currentId% ".
                "\n耗时: %elapsed:6s%/%estimated:-6s% ".
                "\n内存: %memory:6s%"

            );

            // 处理第一页数据
            $this->processTemplateData($firstData['template_list'], $progressBar);

            // 获取下一页token
            $pageToken = $firstData['next_page_token'] ?? '';

            // 循环处理后续页面
            while ($pageToken && $hasMore) {
                $url      = 'https://api.vidu.cn/ent/v1/template-scenes?pager.page_token='.$pageToken.'&pager.pagesz='.$this->batchSize;
                $response = $client->get($url);
                $data     = json_decode($response->getBody()->getContents(), true);

                if (empty($data['template_list'])) {
                    $hasMore = false;
                    continue;
                }

                $this->processTemplateData($data['template_list'], $progressBar);

                $pageToken = $data['next_page_token'] ?? '';
            }

            $progressBar->finish();
            $this->info("\n导入完成，共导入 {$this->totalProcessed} 个模板");
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    private function processTemplateData($templateList, $progressBar): void
    {
        foreach ($templateList as $template) {
            $category = $template['category'];

            if ($category['id'] > 0) {
                $categoryModel = PluginViduTemplateCategory::updateOrCreate([
                    'name' => $category['name'],
                ]);

                $scenes = $template['scenes'];
                foreach ($scenes as $scene) {
                    $scene_name = $scene['name'];
                    $scene_key  = $scene['detail']['scene'];
                    $request    = $scene['api_example']['request'];
                    $score      = $scene['detail']['credit_cost'] ?? 0;
                    $prompt     = '';
                    if (preg_match('/"prompt":\s*"([^"]+)"/', $request, $matches)) {
                        $prompt = $matches[1];
                    }

                    // 下载并上传视频和封面
                    $video_url = $this->downloadAndUploadToOss($scene['video_url'], 'video');
                    $cover_url = $this->downloadAndUploadToOss($scene['cover_url'], 'cover');

                    $templateModel = PluginViduTemplate::updateOrCreate([
                        'name'  => $scene_name,
                        'scene' => $scene_key,
                    ], [
                        'vidu_template_category_id' => $categoryModel->id,
                        'detail'                    => $scene['detail'],
                        'input_instruction'         => $scene['input_instruction'],
                        'prompt'                    => $prompt,
                        'video_url'                 => $video_url,
                        'cover_url'                 => $cover_url,
                        'score'                     => $score,
                    ]);

                    $progressBar->setMessage((string) $templateModel->id, 'currentId');
                    $progressBar->advance(1);
                    $progressBar->display();
                }
                $this->totalProcessed += count($scenes);
            }
        }
    }

    private function downloadAndUploadToOss($url, $type = 'video'): string
    {
        try {
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'vidu_');

            // 下载文件
            $client = new GuzzleClient();
            $client->get($url, ['sink' => $tempFile]);

            // 获取文件扩展名
            $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
            if (empty($extension)) {
                $extension = $type == 'video' ? 'mp4' : 'jpg';
            }

            // 生成OSS路径
            $filename = Str::random(40).'.'.$extension;
            $ossPath  = sprintf("vidu/template/%s",
                $type,
            );
            $filePath = sprintf('%s/%s', $ossPath, $filename);

            // 上传到OSS
            Storage::disk('oss')->put($ossPath, file_get_contents($tempFile));
            if (! Storage::put($filePath, file_get_contents($tempFile))) {
                throw new Exception('下载文件失败'.$url);
            }
            // 删除临时文件
            @unlink($tempFile);

            // 返回完整的OSS URL
            return Storage::url($filePath);
        } catch (Exception $e) {
            $this->error("文件处理失败: {$url}, 错误: ".$e->getMessage());
            return $url;
        }
    }
}