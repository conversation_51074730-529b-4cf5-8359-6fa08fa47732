<?php

namespace App\Packages\AlibabaCloud\Models;

use App\Packages\AlibabaCloud\Config\BailianConfig;
use Exception;

class Volcengine extends BailianConfig
{
    public function __construct()
    {
        parent::__construct('https://ark.cn-beijing.volces.com/api/v3/chat/completions');
        $this->apiKey = 'd7559878-baec-4a6f-8d76-8d7d04a5582e';
    }

    public function chatFormPrompt(string $prompt, string $model, string $content, $maxToken = 4096)
    {
        $system = wateGetSystemPrompt($prompt);
        $data   = [
            'model'      => $model,
            'messages'   => [
                [
                    "role"    => "system",
                    "content" => $system
                ],
                [
                    "role"    => "user",
                    "content" => $content
                ],
            ],
            'max_tokens' => $maxToken,
            'thinking'   => [
                'type' => 'auto'
            ],
        ];
        try {
            $result = $this->otherJson('', $data, [
                'Authorization' => 'Bearer '.$this->apiKey,
            ]);
            if ($result['status']) {
                return $result['data']['choices'][0]['message']['content'];
            } else {
                return $content;
            }
        } catch (Exception $exception) {
            return $content;
        }
    }
}