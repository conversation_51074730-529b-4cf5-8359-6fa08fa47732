<?php

namespace App\Packages\AlibabaCloud\Models;

use App\Packages\AlibabaCloud\Config\BailianConfig;
use Exception;

class TongyiAi extends BailianConfig
{
    public function __construct()
    {
        parent::__construct('https://dashscope.aliyuncs.com/compatible-mode/v1/');
    }

    public function mailContent(string $content, string $model, int $maxToken = 4096)
    {
        $system = wateGetSystemPrompt('EmailContentOptimization');
        $data   = [
            'model'           => $model,
            'messages'        => [
                [
                    "role"    => "system",
                    "content" => $system
                ],
                [
                    "role"    => "user",
                    "content" => $content
                ],
            ],
            'max_tokens'      => $maxToken,
            'enable_thinking' => false,
        ];
        try {
            $result = $this->json('chat/completions', $data);
            if ($result['status']) {
                return $result['data']['choices'][0]['message']['content'];
            } else {
                return $content;
            }
        } catch (Exception $exception) {
            return $content;
        }
    }

    /**
     * 翻译
     * 中文：Chinese
     * 英语：English
     * 日语：Japanese
     * 韩语：Korean
     * 泰语：Thai
     * 法语：French
     * 德语：German
     * 西班牙语：Spanish
     * 阿拉伯语：Arabic
     * 印尼语：Indonesian
     * 越南语：Vietnamese
     * 巴西葡萄牙语：Portuguese
     * 意大利语：Italian
     * 荷兰语：Dutch
     * 俄语：Russian
     * 高棉语：Khmer
     * 宿务语：Cebuano
     * 菲律宾语：Filipino
     * 捷克语：Czech
     * 波兰语：Polish
     * 波斯语：Persian
     * 希伯来语：Hebrew
     * 土耳其语：Turkish
     * 印地语：Hindi
     * 孟加拉语：Bengali
     * 乌尔都语：Urdu
     *
     * @param  string  $text  文本
     * @param  string  $form  来源语言
     * @param  string  $to  目标语言
     * @param  string  $domains  场景说明
     * @return mixed
     * @throws \Exception
     */
    public function mt(string $text, string $form = 'auto', string $to = 'Chinese', string $domains = '')
    {
        $data = [
            'model'               => 'qwen-mt-turbo',
            'messages'            => [
                [
                    "role"    => "user",
                    "content" => $text
                ]
            ],
            'translation_options' => [
                "source_lang" => $form,
                "target_lang" => $to
            ],
        ];
        try {
            $result = $this->json('chat/completions', $data);
            if ($result['status']) {
                return $result['data']['choices'][0]['message']['content'];
            } else {
                info($result['message']);
                return $text;
            }
        } catch (Exception $exception) {
            info($exception);
            return $text;
        }
    }
}