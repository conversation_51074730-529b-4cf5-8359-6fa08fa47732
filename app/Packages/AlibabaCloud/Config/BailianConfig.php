<?php

namespace App\Packages\AlibabaCloud\Config;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class BailianConfig
{
    protected string $apiKey;
    protected Client $client;

    public function __construct(string $baseUrl = 'https://dashscope.aliyuncs.com/api/v1/')
    {
        $this->apiKey = 'sk-e6e9cca6b80647e2b67110871554aeea';

        $this->client = new Client([
            'verify'   => false,
            'base_uri' => $baseUrl,
        ]);
    }

    protected function jsonAsync(string $url, array $data = [])
    {
        return $this->json($url, $data, true);
    }

    protected function json(string $url, array $data = [], bool $isAsync = false)
    {
        $headers = $this->getHeader();
        if ($isAsync) {
            $headers['X-DashScope-Async'] = 'enable';
        }
        try {
            $response = $this->client->post($url, [
                'headers' => $headers,
                'json'    => $data,
            ]);
            if ($response->getStatusCode() == 200) {
                return [
                    'status'  => true,
                    'data'    => json_decode($response->getBody()->getContents(), true),
                    'message' => '',
                ];
            } else {
                $data = json_decode($response->getBody()->getContents(), true);
                return [
                    'status'  => false,
                    'data'    => [],
                    'message' => $data['message'],
                ];
            }
        } catch (RequestException $exception) {
            $error = json_decode($exception->getResponse()->getBody()->getContents(), true);
            return [
                'status'  => false,
                'data'    => [],
                'message' => $error['message'],
            ];
        } catch (Exception $exception) {
            return [
                'status'  => false,
                'data'    => [],
                'message' => $exception->getMessage(),
            ];
        }
    }

    protected function getHeader()
    {
        return [
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer '.$this->apiKey,
        ];
    }

    protected function otherJson(string $url, array $data = [], array $headers = [])
    {
        try {
            $response = $this->client->post($url, [
                'headers' => $headers,
                'json'    => $data,
            ]);
            if ($response->getStatusCode() == 200) {
                return [
                    'status'  => true,
                    'data'    => json_decode($response->getBody()->getContents(), true),
                    'message' => '',
                ];
            } else {
                $data = json_decode($response->getBody()->getContents(), true);
                return [
                    'status'  => false,
                    'data'    => [],
                    'message' => $data['message'],
                ];
            }
        } catch (RequestException $exception) {
            $error   = json_decode($exception->getResponse()->getBody()->getContents(), true);
            $message = $error['message'] ?? '';
            if (blank($message)) {
                $message = $error['error']['message'] ?? '';
            }
            return [
                'status'  => false,
                'data'    => [],
                'message' => $message,
            ];
        } catch (Exception $exception) {
            return [
                'status'  => false,
                'data'    => [],
                'message' => $exception->getMessage(),
            ];
        }
    }

    protected function get(string $url, array $query = [])
    {
        $headers = $this->getHeader();

        try {
            $response = $this->client->post($url, [
                'headers' => $headers,
                'query'   => $query,
            ]);
            if ($response->getStatusCode() == 200) {
                return [
                    'status'  => true,
                    'data'    => json_decode($response->getBody()->getContents(), true),
                    'message' => '',
                ];
            } else {
                $data = json_decode($response->getBody()->getContents(), true);
                return [
                    'status'  => false,
                    'data'    => [],
                    'message' => $data['message'],
                ];
            }
        } catch (Exception $exception) {
            return [
                'status'  => false,
                'data'    => [],
                'message' => $exception->getMessage(),
            ];
        }
    }
}