<?php

namespace App\Admin\Actions\Vidu;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Support\Facades\Artisan;

class SyncTemplatesAction extends AbstractTool
{
    protected string $title = '同步模板';

    protected string $style = 'btn btn-primary';

    public function handle(): Response
    {
        try {
            // 调用 Artisan 命令同步模板
            Artisan::call('skyxu:vidu-import-templates');

            return $this->response()->success('模板同步成功！')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('同步失败：'.$e->getMessage());
        }
    }

    public function confirm(): array
    {
        return [
            '同步模板',
            '确定要从 Vidu 同步最新的模板数据吗？此操作可能需要一些时间。',
        ];
    }
}
