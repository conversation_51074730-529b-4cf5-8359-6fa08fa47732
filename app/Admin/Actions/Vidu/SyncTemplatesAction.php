<?php

namespace App\Admin\Actions\Vidu;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Support\Facades\Artisan;
use Symfony\Component\Console\Output\BufferedOutput;

class SyncTemplatesAction extends AbstractTool
{
    protected string $title = '同步模板';

    protected string $style = 'btn btn-primary';

    /**
     * 渲染按钮，添加加载状态
     */
    public function render(): string
    {
        $this->setupScript();

        return parent::render();
    }

    /**
     * 设置JavaScript脚本，添加加载状态
     */
    protected function setupScript(): void
    {
        $script = <<<JS
        // 监听确认对话框的确认按钮点击事件
        $(document).on('click', '.swal2-confirm', function() {
            // 检查是否是同步模板的确认对话框
            if ($('.swal2-title').text().includes('同步模板')) {
                var btn = $('.{$this->getElementClass()}');

                // 显示加载状态
                btn.prop('disabled', true)
                   .html('<i class="fa fa-spinner fa-spin"></i> 正在同步模板...')
                   .removeClass('btn-primary')
                   .addClass('btn-warning');

                // 添加全局提示
                Dcat.success('开始同步模板，请耐心等待...', '', {timeOut: 3000});
            }
        });
JS;

        \Dcat\Admin\Admin::script($script);
    }

    public function handle(): Response
    {
        try {
            // 创建缓冲输出以捕获实时输出
            $output = new BufferedOutput();

            // 调用 Artisan 命令同步模板，传入输出缓冲区
            $exitCode = Artisan::call('skyxu:vidu-import-templates', [], $output);

            // 获取命令输出
            $outputContent = $output->fetch();

            if ($exitCode === 0) {
                // 命令执行成功
                $message = '✅ 模板同步成功！';
                if (!empty(trim($outputContent))) {
                    // 格式化输出，保留换行和进度信息
                    $formattedOutput = $this->formatOutput($outputContent);
                    $message .= '<br><div style="background:#f8f9fa;padding:10px;border-radius:4px;margin-top:10px;font-family:monospace;"><small>' . $formattedOutput . '</small></div>';
                }
                return $this->response()->success($message)->refresh();
            } else {
                // 命令执行失败
                $errorMessage = '❌ 同步命令执行失败（退出码：' . $exitCode . '）';
                if (!empty(trim($outputContent))) {
                    $formattedOutput = $this->formatOutput($outputContent);
                    $errorMessage .= '<br><div style="background:#fff2f0;padding:10px;border-radius:4px;margin-top:10px;font-family:monospace;"><small>' . $formattedOutput . '</small></div>';
                }
                return $this->response()->error($errorMessage);
            }
        } catch (\Exception $e) {
            return $this->response()->error('❌ 同步失败：'.$e->getMessage());
        }
    }

    /**
     * 格式化命令输出
     */
    private function formatOutput(string $output): string
    {
        // 转义HTML特殊字符
        $output = htmlspecialchars($output);

        // 保留换行
        $output = nl2br($output);

        // 高亮重要信息
        $output = preg_replace('/^(开始导入.*)/m', '<strong style="color:#1890ff;">$1</strong>', $output);
        $output = preg_replace('/^(导入完成.*)/m', '<strong style="color:#52c41a;">$1</strong>', $output);
        $output = preg_replace('/(\d+\/\d+)/', '<span style="color:#fa8c16;">$1</span>', $output);
        $output = preg_replace('/(\d+%)/', '<span style="color:#722ed1;">$1</span>', $output);

        return $output;
    }

    public function confirm(): array
    {
        return [
            '同步模板',
            '确定要从 Vidu 同步最新的模板数据吗？此操作可能需要一些时间。',
        ];
    }
}
