<?php

namespace App\Admin\Actions\Vidu;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Support\Facades\Artisan;

class SyncTemplatesAction extends AbstractTool
{
    protected string $title = '同步模板';

    protected string $style = 'btn btn-primary';

    public function handle(): Response
    {
        try {
            // 调用 Artisan 命令同步模板
            $exitCode = Artisan::call('skyxu:vidu-import-templates');

            // 获取命令输出
            $output = Artisan::output();

            if ($exitCode === 0) {
                // 命令执行成功
                $message = '模板同步成功！';
                if (!empty(trim($output))) {
                    $message .= '<br><small>执行结果：' . nl2br(htmlspecialchars(trim($output))) . '</small>';
                }
                return $this->response()->success($message)->refresh();
            } else {
                // 命令执行失败
                $errorMessage = '同步命令执行失败（退出码：' . $exitCode . '）';
                if (!empty(trim($output))) {
                    $errorMessage .= '<br><small>' . nl2br(htmlspecialchars(trim($output))) . '</small>';
                }
                return $this->response()->error($errorMessage);
            }
        } catch (\Exception $e) {
            return $this->response()->error('同步失败：'.$e->getMessage());
        }
    }

    public function confirm(): array
    {
        return [
            '同步模板',
            '确定要从 Vidu 同步最新的模板数据吗？此操作可能需要一些时间。',
        ];
    }
}
