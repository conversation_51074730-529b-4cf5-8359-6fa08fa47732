<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offline_tasks', function (Blueprint $table) {
            $table->uuid('id')->primary()->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->string('command')->comment('离线任务命令');
            $table->unsignedInteger('minutes')->nullable(1)->comment('离线任务执行间隔分钟数');
            $table->json('params')->comment('离线任务参数');
            $table->boolean('status')->default(0)->comment('任务状态');
            $table->timestamp('next_at')->nullable()->comment('下一次任务执行时间');
            $table->timestamp('last_at')->nullable()->comment('上一次任务执行时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offline_tasks');
    }
};
